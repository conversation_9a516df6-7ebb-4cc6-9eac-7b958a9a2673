// Emplacement : src/ClipboardPlus.Tests.Integration/TestServices/TestClipboardInteractionService.cs
using ClipboardPlus.Core.Services.Interfaces;
using System.Threading.Tasks;

public class TestClipboardInteractionService : IClipboardInteractionService
{
    private string _simulatedText = "";

    // Méthode de test pour définir le contenu du "faux" presse-papiers
    public void SetSimulatedTextContent(string text)
    {
        _simulatedText = text;
    }

    // Implémentation de l'interface
    public Task<string?> GetTextAsync()
    {
        return Task.FromResult<string?>(_simulatedText);
    }

    public Task<bool> SetClipboardContentAsync(string text)
    {
        _simulatedText = text;
        return Task.FromResult(true);
    }
    
    // ... autres méthodes de l'interface implémentées pour retourner des valeurs par défaut ...
    public bool ContainsText() => !string.IsNullOrEmpty(_simulatedText);
    // ...
}