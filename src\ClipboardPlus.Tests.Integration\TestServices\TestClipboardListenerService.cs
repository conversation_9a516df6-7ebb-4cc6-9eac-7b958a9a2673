// Emplacement : src/ClipboardPlus.Tests.Integration/TestServices/TestClipboardListenerService.cs
using ClipboardPlus.Core.Services;
using System;

public class TestClipboardListenerService : IClipboardListenerService
{
    public event EventHandler? ClipboardContentChanged;

    public bool IsListening { get; private set; }

    public bool StartListening()
    {
        IsListening = true;
        return true;
    }

    public void StopListening()
    {
        IsListening = false;
    }

    // La méthode clé pour nos tests !
    public void SimulateClipboardChange()
    {
        ClipboardContentChanged?.Invoke(this, EventArgs.Empty);
    }

    public void Dispose() { }
}