// Emplacement : src/ClipboardPlus.Tests.Integration/ApplicationWiringTests.cs

using NUnit.Framework;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services.Configuration;
using ClipboardPlus.Core.DataModels;
using System.Linq;
using ClipboardPlus.Core.Services.Interfaces;
using ClipboardPlus.Tests.Integration.TestServices; // Important !

namespace ClipboardPlus.Tests.Integration
{
    [TestFixture]
    public class ApplicationWiringTests
    {
        private IServiceProvider _serviceProvider = null!;
        private IClipboardHistoryManager _historyManager = null!;
        private IPersistenceService _persistenceService = null!;

        [SetUp]
        public void SetUp()
        {
            // --- C'est ici que la magie opère ---

            // 1. Charger la VRAIE configuration de l'application
            var services = new ServiceCollection();
            HostConfiguration.AddServices(services); // On utilise la méthode qui expose la collection

            // 2. Appliquer les "patchs chirurgicaux" INDISPENSABLES pour le découplage
            // On remplace les services qui parlent au système d'exploitation par nos doubles de test
            
            // Remplacement du Dispatcher
            var dispatcherDescriptor = services.FirstOrDefault(d => d.ServiceType == typeof(IDispatcherService));
            if (dispatcherDescriptor != null) services.Remove(dispatcherDescriptor);
            services.AddSingleton<IDispatcherService, TestDispatcherService>();

            // Remplacement du Listener
            var listenerDescriptor = services.FirstOrDefault(d => d.ServiceType == typeof(IClipboardListenerService));
            if (listenerDescriptor != null) services.Remove(listenerDescriptor);
            services.AddSingleton<IClipboardListenerService, TestClipboardListenerService>();

            // Remplacement de l'Interaction Clipboard
            var interactionDescriptor = services.FirstOrDefault(d => d.ServiceType == typeof(IClipboardInteractionService));
            if (interactionDescriptor != null) services.Remove(interactionDescriptor);
            services.AddSingleton<IClipboardInteractionService, TestClipboardInteractionService>();

            // 3. Construire le conteneur de services final
            _serviceProvider = services.BuildServiceProvider();

            // 4. Récupérer les services nécessaires pour les tests
            _historyManager = _serviceProvider.GetRequiredService<IClipboardHistoryManager>();
            _persistenceService = _serviceProvider.GetRequiredService<IPersistenceService>();

            // 5. Assurer un état propre avant chaque test
            _persistenceService.ExecuteNonQueryAsync("DELETE FROM ClipboardItems;").GetAwaiter().GetResult();
        }

        [Test]
        [Description("Given que le service d'écoute est actif, When le contenu du presse-papiers change, Then un nouvel élément doit être persisté en base de données.")]
        public async Task AutomaticCapture_WhenClipboardChanges_ShouldPersistNewItem()
        {
            // --- Arrange ---
            
            // Récupérer les services de test spécifiques
            var listener = _serviceProvider.GetRequiredService<IClipboardListenerService>() as TestClipboardListenerService;
            var interaction = _serviceProvider.GetRequiredService<IClipboardInteractionService>() as TestClipboardInteractionService;
            
            Assert.That(listener, Is.Not.Null, "Le TestClipboardListenerService doit être résolvable.");
            Assert.That(interaction, Is.Not.Null, "Le TestClipboardInteractionService doit être résolvable.");
            
            // Démarrer l'écoute
            listener!.StartListening();
            
            // Définir le contenu du "faux" presse-papiers
            var textContent = "Ceci est un test End-to-End.";
            interaction!.SetSimulatedTextContent(textContent);
            
            var initialItems = await _historyManager.LoadHistorySilentlyAsync();
            var initialCount = initialItems.Count;

            // --- Act ---
            
            // Simuler l'événement "Ctrl+C" de l'utilisateur. C'est le point d'entrée de haut niveau.
            listener.SimulateClipboardChange();
            
            // Laisser le temps au traitement asynchrone de se terminer
            await Task.Delay(200);

            // --- Assert ---
            
            // Recharger l'historique depuis la source de vérité (la base de données)
            var finalItems = await _historyManager.LoadHistorySilentlyAsync();
            var finalCount = finalItems.Count;
            
            // 1. Vérifier que le nombre d'éléments a augmenté de 1
            Assert.That(finalCount, Is.EqualTo(initialCount + 1), "Le nombre d'éléments dans l'historique aurait dû augmenter de 1.");
            
            // 2. Vérifier que le nouvel élément a le bon contenu
            var newItem = finalItems.OrderByDescending(i => i.Id).FirstOrDefault();
            Assert.That(newItem, Is.Not.Null, "Un nouvel élément aurait dû être créé.");
            Assert.That(newItem.TextPreview, Is.EqualTo(textContent), "Le contenu du nouvel élément est incorrect.");
            Assert.That(newItem.DataType, Is.EqualTo(ClipboardDataType.Text), "Le type de données du nouvel élément est incorrect.");
        }
    }
}